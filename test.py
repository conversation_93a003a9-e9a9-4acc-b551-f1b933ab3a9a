# import base64
# from openai import OpenAI
# import pandas as pd
# import io
# from io import BytesIO
# import base64
# # from PIL import Image
# from tqdm import tqdm
# # import openpyxl
# # from openpyxl.drawing.image import Image as image
# import json
# # from PIL import Image as PILImage
# import ast
# import requests
# # from openpyxl import Workbook
# # from openpyxl.drawing.image import Image
# import os


# def image_to_base64(image_path): #base64是个字符串
#     with open(image_path, "rb") as image_file:
#         encoded_string = base64.b64encode(image_file.read())
#     return encoded_string.decode('utf-8')


# client = OpenAI(api_key='YOUR_API_KEY', base_url='http://10.8.25.27:2556/v1')
# # Encode local image to base64


# # Get model name
# model_name = client.models.list().data[0].id
# def intervl_request(qs,image_base64,model_name):
#     # Create chat completion request
#     response = client.chat.completions.create(
#         model=model_name,
#         messages=[
#             {
#             'role': 'user',
#             'content': [{"type": "text","text": f"{qs}"},
#              {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"}}],


#         }],
#         temperature=0.1,
#         top_p=0.8,
#         max_tokens=512
#     )

#     # Print the response
#     return response.choices[0].message.content
# print(model_name)
# print(intervl_request("请你描述图片，简短40个字回答",image_to_base64(r"/Users/<USER>/projects/data/D4LA/images/specification_87276759.png"),model_name))

from openai import OpenAI
client = OpenAI(
base_url="http://10.8.25.27:11421/v1",
api_key="notneeded"
)
response = client.chat.completions.create(
model="qwen2.5:7b",
messages=[
{"role": "user", "content": "如何科学减肥"}
],
temperature=0.7
)
print(response.choices[0].message.content)