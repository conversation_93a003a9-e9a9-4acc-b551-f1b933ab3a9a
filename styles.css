body {
    font-family: 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

h1 {
    color: #2c3e50;
    text-align: center;
    border-bottom: 2px solid #eee;
    padding-bottom: 10px;
}

h2, h3 {
    color: #2c3e50;
    margin-top: 0;
}

.question-box {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 5px;
    margin: 20px 0;
}

.hint-box {
    background-color: #e3f2fd;
    padding: 15px;
    border-radius: 5px;
    margin: 20px 0;
}

.hint-box ul {
    margin: 10px 0;
    padding-left: 20px;
}

.solution {
    background-color: #fff3e0;
    padding: 20px;
    border-radius: 5px;
    margin: 20px 0;
}

.image-container {
    text-align: center;
    margin: 20px 0;
}

.puzzle-image, .solution-image {
    width: 100%;
    background-color: #fff;
    border: 2px solid #ddd;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.puzzle-image {
    max-width: 400px;
    height: auto;
}

.solution-image {
    max-width: 200px;
    height: auto;
}

.button-container {
    text-align: center;
    margin: 20px 0;
    display: flex;
    justify-content: center;
    gap: 20px;
}

button {
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.solution-btn {
    background-color: #4CAF50;
}

.solution-btn:hover {
    background-color: #45a049;
}

.new-question-btn {
    background-color: #2196F3;
}

.new-question-btn:hover {
    background-color: #1976D2;
} 