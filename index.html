<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空间智力题</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h1>空间智力题</h1>
        
        <div class="question-box" id="question-container">
            <!-- 题目内容将通过 JavaScript 动态生成 -->
        </div>

        <div class="hint-box" id="hint-container">
            <!-- 提示内容将通过 JavaScript 动态生成 -->
        </div>

        <div class="button-container">
            <button onclick="showSolution()" class="solution-btn">显示解答</button>
            <button onclick="generateNewQuestion()" class="new-question-btn">下一题</button>
        </div>

        <div class="solution" id="solution" style="display: none">
            <!-- 解答内容将通过 JavaScript 动态生成 -->
        </div>
    </div>

    <script>
        // 题目数据库
        const questions = [
            {
                id: 1,
                title: "立方体展开图谜题",
                description: "下面是一个立方体的展开图，请判断：当按照虚线折叠成立方体后，标有"X"的面会与标有"O"的面相邻吗？",
                puzzleSvg: `
                    <svg class="puzzle-image" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
                        <rect x="100" y="100" width="60" height="60" fill="white" stroke="black"/>
                        <rect x="160" y="100" width="60" height="60" fill="white" stroke="black"/>
                        <rect x="220" y="100" width="60" height="60" fill="white" stroke="black"/>
                        <rect x="160" y="40" width="60" height="60" fill="white" stroke="black"/>
                        <text x="190" y="80" text-anchor="middle" font-size="24">X</text>
                        <rect x="160" y="160" width="60" height="60" fill="white" stroke="black"/>
                        <text x="190" y="200" text-anchor="middle" font-size="24">O</text>
                        <path d="M160,40 L160,220" stroke="black" stroke-dasharray="5,5"/>
                        <path d="M220,40 L220,220" stroke="black" stroke-dasharray="5,5"/>
                        <path d="M100,100 L280,100" stroke="black" stroke-dasharray="5,5"/>
                        <path d="M100,160 L280,160" stroke="black" stroke-dasharray="5,5"/>
                    </svg>
                `,
                hints: [
                    "仔细观察展开图的方向",
                    "想象折叠的过程",
                    "考虑每个面的相对位置"
                ],
                solution: {
                    answer: "不会相邻。",
                    explanation: [
                        "按照展开图折叠时，标有"X"的面会成为顶面",
                        "标有"O"的面会成为底面",
                        "在立方体中，顶面和底面是相对的面，它们之间隔着一层，因此不可能相邻"
                    ],
                    solutionSvg: `
                        <svg class="solution-image" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                            <path d="M50,150 L150,150 L150,50 L50,50 Z" fill="none" stroke="black"/>
                            <path d="M50,50 L75,25 L175,25 L150,50" fill="none" stroke="black"/>
                            <path d="M150,150 L175,125 L175,25" fill="none" stroke="black"/>
                            <text x="100" y="40" text-anchor="middle" font-size="16">X</text>
                            <text x="100" y="140" text-anchor="middle" font-size="16">O</text>
                        </svg>
                    `
                }
            },
            {
                id: 2,
                title: "立方体旋转题",
                description: "一个立方体的三个可见面如图所示，将立方体向右旋转90度后，标有"△"的面会出现在哪个位置？",
                puzzleSvg: `
                    <svg class="puzzle-image" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <path d="M50,150 L150,150 L150,50 L50,50 Z" fill="white" stroke="black"/>
                        <path d="M50,50 L75,25 L175,25 L150,50" fill="white" stroke="black"/>
                        <path d="M150,150 L175,125 L175,25" fill="white" stroke="black"/>
                        <text x="100" y="100" text-anchor="middle" font-size="24">△</text>
                    </svg>
                `,
                hints: [
                    "注意观察立方体的旋转方向",
                    "思考旋转后各个面的位置变化",
                    "记住立方体的三维特性"
                ],
                solution: {
                    answer: "旋转后，△会出现在右侧面。",
                    explanation: [
                        "向右旋转90度时，正面会变成左侧面",
                        "原来的右侧面会变成正面",
                        "标有△的面会移动到右侧"
                    ],
                    solutionSvg: `
                        <svg class="solution-image" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                            <path d="M50,150 L150,150 L150,50 L50,50 Z" fill="white" stroke="black"/>
                            <path d="M50,50 L75,25 L175,25 L150,50" fill="white" stroke="black"/>
                            <path d="M150,150 L175,125 L175,25" fill="white" stroke="black"/>
                            <text x="160" y="85" text-anchor="middle" font-size="24">△</text>
                        </svg>
                    `
                }
            }
        ];

        let currentQuestionIndex = 0;

        function generateNewQuestion() {
            // 隐藏解答
            document.getElementById('solution').style.display = 'none';
            
            // 随机选择一个新的题目索引
            let newIndex;
            do {
                newIndex = Math.floor(Math.random() * questions.length);
            } while (newIndex === currentQuestionIndex && questions.length > 1);
            
            currentQuestionIndex = newIndex;
            displayQuestion(questions[currentQuestionIndex]);
        }

        function displayQuestion(question) {
            // 显示题目
            const questionContainer = document.getElementById('question-container');
            questionContainer.innerHTML = `
                <h2>题目：${question.title}</h2>
                <p>${question.description}</p>
                <div class="image-container">
                    ${question.puzzleSvg}
                </div>
            `;

            // 显示提示
            const hintContainer = document.getElementById('hint-container');
            hintContainer.innerHTML = `
                <h3>提示：</h3>
                <ul>
                    ${question.hints.map(hint => `<li>${hint}</li>`).join('')}
                </ul>
            `;

            // 准备解答内容
            const solutionContainer = document.getElementById('solution');
            solutionContainer.innerHTML = `
                <h3>解答：</h3>
                <p>${question.solution.answer}</p>
                <p>解释：</p>
                <ol>
                    ${question.solution.explanation.map(exp => `<li>${exp}</li>`).join('')}
                </ol>
                <div class="image-container">
                    ${question.solution.solutionSvg}
                </div>
            `;
        }

        function showSolution() {
            document.getElementById('solution').style.display = 'block';
        }

        // 页面加载时显示第一个题目
        window.onload = function() {
            generateNewQuestion();
        };
    </script>
</body>
</html> 